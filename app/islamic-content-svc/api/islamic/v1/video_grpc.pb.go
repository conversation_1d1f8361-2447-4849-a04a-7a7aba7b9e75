// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.31.1
// source: islamic/v1/video.proto

package islamicv1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	VideoService_PlaylistList_FullMethodName         = "/islamic.v1.VideoService/PlaylistList"
	VideoService_VideoList_FullMethodName            = "/islamic.v1.VideoService/VideoList"
	VideoService_VideoDetail_FullMethodName          = "/islamic.v1.VideoService/VideoDetail"
	VideoService_RecommendedVideoList_FullMethodName = "/islamic.v1.VideoService/RecommendedVideoList"
	VideoService_VideoCollect_FullMethodName         = "/islamic.v1.VideoService/VideoCollect"
	VideoService_VideoCollectList_FullMethodName     = "/islamic.v1.VideoService/VideoCollectList"
)

// VideoServiceClient is the client API for VideoService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type VideoServiceClient interface {
	// 视频playlist
	PlaylistList(ctx context.Context, in *PlaylistListReq, opts ...grpc.CallOption) (*PlaylistListRes, error)
	// 视频相关
	VideoList(ctx context.Context, in *VideoListReq, opts ...grpc.CallOption) (*VideoListRes, error)
	VideoDetail(ctx context.Context, in *VideoDetailReq, opts ...grpc.CallOption) (*VideoDetailRes, error)
	RecommendedVideoList(ctx context.Context, in *RecommendedVideoListReq, opts ...grpc.CallOption) (*RecommendedVideoListRes, error)
	// 视频收藏
	VideoCollect(ctx context.Context, in *VideoCollectReq, opts ...grpc.CallOption) (*VideoCollectRes, error)
	VideoCollectList(ctx context.Context, in *VideoCollectListReq, opts ...grpc.CallOption) (*VideoCollectListRes, error)
}

type videoServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewVideoServiceClient(cc grpc.ClientConnInterface) VideoServiceClient {
	return &videoServiceClient{cc}
}

func (c *videoServiceClient) PlaylistList(ctx context.Context, in *PlaylistListReq, opts ...grpc.CallOption) (*PlaylistListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PlaylistListRes)
	err := c.cc.Invoke(ctx, VideoService_PlaylistList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoServiceClient) VideoList(ctx context.Context, in *VideoListReq, opts ...grpc.CallOption) (*VideoListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VideoListRes)
	err := c.cc.Invoke(ctx, VideoService_VideoList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoServiceClient) VideoDetail(ctx context.Context, in *VideoDetailReq, opts ...grpc.CallOption) (*VideoDetailRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VideoDetailRes)
	err := c.cc.Invoke(ctx, VideoService_VideoDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoServiceClient) RecommendedVideoList(ctx context.Context, in *RecommendedVideoListReq, opts ...grpc.CallOption) (*RecommendedVideoListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RecommendedVideoListRes)
	err := c.cc.Invoke(ctx, VideoService_RecommendedVideoList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoServiceClient) VideoCollect(ctx context.Context, in *VideoCollectReq, opts ...grpc.CallOption) (*VideoCollectRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VideoCollectRes)
	err := c.cc.Invoke(ctx, VideoService_VideoCollect_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoServiceClient) VideoCollectList(ctx context.Context, in *VideoCollectListReq, opts ...grpc.CallOption) (*VideoCollectListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VideoCollectListRes)
	err := c.cc.Invoke(ctx, VideoService_VideoCollectList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VideoServiceServer is the server API for VideoService service.
// All implementations must embed UnimplementedVideoServiceServer
// for forward compatibility.
type VideoServiceServer interface {
	// 视频playlist
	PlaylistList(context.Context, *PlaylistListReq) (*PlaylistListRes, error)
	// 视频相关
	VideoList(context.Context, *VideoListReq) (*VideoListRes, error)
	VideoDetail(context.Context, *VideoDetailReq) (*VideoDetailRes, error)
	RecommendedVideoList(context.Context, *RecommendedVideoListReq) (*RecommendedVideoListRes, error)
	// 视频收藏
	VideoCollect(context.Context, *VideoCollectReq) (*VideoCollectRes, error)
	VideoCollectList(context.Context, *VideoCollectListReq) (*VideoCollectListRes, error)
	mustEmbedUnimplementedVideoServiceServer()
}

// UnimplementedVideoServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedVideoServiceServer struct{}

func (UnimplementedVideoServiceServer) PlaylistList(context.Context, *PlaylistListReq) (*PlaylistListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PlaylistList not implemented")
}
func (UnimplementedVideoServiceServer) VideoList(context.Context, *VideoListReq) (*VideoListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VideoList not implemented")
}
func (UnimplementedVideoServiceServer) VideoDetail(context.Context, *VideoDetailReq) (*VideoDetailRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VideoDetail not implemented")
}
func (UnimplementedVideoServiceServer) RecommendedVideoList(context.Context, *RecommendedVideoListReq) (*RecommendedVideoListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecommendedVideoList not implemented")
}
func (UnimplementedVideoServiceServer) VideoCollect(context.Context, *VideoCollectReq) (*VideoCollectRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VideoCollect not implemented")
}
func (UnimplementedVideoServiceServer) VideoCollectList(context.Context, *VideoCollectListReq) (*VideoCollectListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VideoCollectList not implemented")
}
func (UnimplementedVideoServiceServer) mustEmbedUnimplementedVideoServiceServer() {}
func (UnimplementedVideoServiceServer) testEmbeddedByValue()                      {}

// UnsafeVideoServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to VideoServiceServer will
// result in compilation errors.
type UnsafeVideoServiceServer interface {
	mustEmbedUnimplementedVideoServiceServer()
}

func RegisterVideoServiceServer(s grpc.ServiceRegistrar, srv VideoServiceServer) {
	// If the following call pancis, it indicates UnimplementedVideoServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&VideoService_ServiceDesc, srv)
}

func _VideoService_PlaylistList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PlaylistListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServiceServer).PlaylistList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoService_PlaylistList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServiceServer).PlaylistList(ctx, req.(*PlaylistListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoService_VideoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VideoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServiceServer).VideoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoService_VideoList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServiceServer).VideoList(ctx, req.(*VideoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoService_VideoDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VideoDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServiceServer).VideoDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoService_VideoDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServiceServer).VideoDetail(ctx, req.(*VideoDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoService_RecommendedVideoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecommendedVideoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServiceServer).RecommendedVideoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoService_RecommendedVideoList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServiceServer).RecommendedVideoList(ctx, req.(*RecommendedVideoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoService_VideoCollect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VideoCollectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServiceServer).VideoCollect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoService_VideoCollect_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServiceServer).VideoCollect(ctx, req.(*VideoCollectReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoService_VideoCollectList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VideoCollectListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServiceServer).VideoCollectList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoService_VideoCollectList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServiceServer).VideoCollectList(ctx, req.(*VideoCollectListReq))
	}
	return interceptor(ctx, in, info, handler)
}

// VideoService_ServiceDesc is the grpc.ServiceDesc for VideoService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var VideoService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "islamic.v1.VideoService",
	HandlerType: (*VideoServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PlaylistList",
			Handler:    _VideoService_PlaylistList_Handler,
		},
		{
			MethodName: "VideoList",
			Handler:    _VideoService_VideoList_Handler,
		},
		{
			MethodName: "VideoDetail",
			Handler:    _VideoService_VideoDetail_Handler,
		},
		{
			MethodName: "RecommendedVideoList",
			Handler:    _VideoService_RecommendedVideoList_Handler,
		},
		{
			MethodName: "VideoCollect",
			Handler:    _VideoService_VideoCollect_Handler,
		},
		{
			MethodName: "VideoCollectList",
			Handler:    _VideoService_VideoCollectList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "islamic/v1/video.proto",
}
