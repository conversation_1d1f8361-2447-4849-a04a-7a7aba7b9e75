// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Videos is the golang structure for table videos.
type Videos struct {
	Id               uint        `json:"id"               orm:"id"                 description:"主键ID"`                   // 主键ID
	CategoryId       uint        `json:"categoryId"       orm:"category_id"        description:"分类ID"`                   // 分类ID
	VideoUrl         string      `json:"videoUrl"         orm:"video_url"          description:"视频文件URL"`                // 视频文件URL
	VideoSize        uint64      `json:"videoSize"        orm:"video_size"         description:"视频文件大小(字节)"`             // 视频文件大小(字节)
	VideoDuration    uint        `json:"videoDuration"    orm:"video_duration"     description:"视频时长(秒)"`                // 视频时长(秒)
	VideoFormat      string      `json:"videoFormat"      orm:"video_format"       description:"视频格式：mp4, mov等"`         // 视频格式：mp4, mov等
	VideoCoverUrl    string      `json:"videoCoverUrl"    orm:"video_cover_url"    description:"视频封面图片URL"`              // 视频封面图片URL
	ViewCount        uint64      `json:"viewCount"        orm:"view_count"         description:"播放次数"`                   // 播放次数
	ShareCount       uint64      `json:"shareCount"       orm:"share_count"        description:"分享次数"`                   // 分享次数
	CollectCount     uint64      `json:"collectCount"     orm:"collect_count"      description:"收藏次数"`                   // 收藏次数
	CreatorName      string      `json:"creatorName"      orm:"creator_name"       description:"创建者姓名"`                  // 创建者姓名
	Author           string      `json:"author"           orm:"author"             description:"视频作者"`                   // 视频作者
	AuthorLogo       string      `json:"authorLogo"       orm:"author_logo"        description:"作者头像URL"`                // 作者头像URL
	AuthorAuthStatus uint        `json:"authorAuthStatus" orm:"author_auth_status" description:"作者认证状态：0-未认证，1-已认证"`     // 作者认证状态：0-未认证，1-已认证
	PublishState     uint        `json:"publishState"     orm:"publish_state"      description:"发布状态：0-待发布，1-已发布，2-已下线"` // 发布状态：0-待发布，1-已发布，2-已下线
	IsRecommended    uint        `json:"isRecommended"    orm:"is_recommended"     description:"是否推荐，0-否，1-是"`           // 是否推荐，0-否，1-是
	CreatedAt        *gtime.Time `json:"createdAt"        orm:"created_at"         description:"创建时间"`                   // 创建时间
	PublishedAt      *gtime.Time `json:"publishedAt"      orm:"published_at"       description:"发布时间"`                   // 发布时间
	UpdatedAt        *gtime.Time `json:"updatedAt"        orm:"updated_at"         description:"更新时间"`                   // 更新时间
}
