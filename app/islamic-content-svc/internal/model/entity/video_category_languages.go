// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// VideoCategoryLanguages is the golang structure for table video_category_languages.
type VideoCategoryLanguages struct {
	Id          uint        `json:"id"          orm:"id"          description:"主键ID"`                 // 主键ID
	CategoryId  uint        `json:"categoryId"  orm:"category_id" description:"分类ID"`                 // 分类ID
	LanguageId  uint        `json:"languageId"  orm:"language_id" description:"语言ID：0-中文，1-英文，2-印尼语"` // 语言ID：0-中文，1-英文，2-印尼语
	Name        string      `json:"name"        orm:"name"        description:"分类名称"`                 // 分类名称
	Description string      `json:"description" orm:"description" description:"分类描述"`                 // 分类描述
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"  description:"创建时间"`                 // 创建时间
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"  description:"更新时间"`                 // 更新时间
}
