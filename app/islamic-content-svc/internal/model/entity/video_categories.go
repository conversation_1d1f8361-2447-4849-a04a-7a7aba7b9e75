// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// VideoCategories is the golang structure for table video_categories.
type VideoCategories struct {
	Id         uint        `json:"id"         orm:"id"          description:"主键ID"`    // 主键ID
	VideoCount uint        `json:"videoCount" orm:"video_count" description:"分类下视频数量"` // 分类下视频数量
	Remark     string      `json:"remark"     orm:"remark"      description:"备注"`      // 备注
	CreatedAt  *gtime.Time `json:"createdAt"  orm:"created_at"  description:"创建时间"`    // 创建时间
	UpdatedAt  *gtime.Time `json:"updatedAt"  orm:"updated_at"  description:"更新时间"`    // 更新时间
}
