// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// VideoPlaylistRelations is the golang structure for table video_playlist_relations.
type VideoPlaylistRelations struct {
	Id         uint        `json:"id"         orm:"id"          description:"主键ID"`         // 主键ID
	PlaylistId uint        `json:"playlistId" orm:"playlist_id" description:"播放列表ID"`       // 播放列表ID
	VideoId    uint        `json:"videoId"    orm:"video_id"    description:"视频ID"`         // 视频ID
	SortOrder  uint        `json:"sortOrder"  orm:"sort_order"  description:"排序权重，数字越小越靠前"` // 排序权重，数字越小越靠前
	CreatedAt  *gtime.Time `json:"createdAt"  orm:"created_at"  description:"创建时间"`         // 创建时间
	UpdatedAt  *gtime.Time `json:"updatedAt"  orm:"updated_at"  description:"更新时间"`         // 更新时间
}
