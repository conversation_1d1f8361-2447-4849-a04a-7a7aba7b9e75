// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// VideoPlayHistory is the golang structure for table video_play_history.
type VideoPlayHistory struct {
	Id           uint64      `json:"id"           orm:"id"            description:"主键ID"`           // 主键ID
	UserId       uint64      `json:"userId"       orm:"user_id"       description:"用户ID"`           // 用户ID
	VideoId      uint        `json:"videoId"      orm:"video_id"      description:"视频ID"`           // 视频ID
	PlayPosition uint        `json:"playPosition" orm:"play_position" description:"播放位置(秒)"`        // 播放位置(秒)
	PlayDuration uint        `json:"playDuration" orm:"play_duration" description:"本次播放时长(秒)"`      // 本次播放时长(秒)
	IsCompleted  uint        `json:"isCompleted"  orm:"is_completed"  description:"是否播放完成，0-否，1-是"` // 是否播放完成，0-否，1-是
	CreatedAt    *gtime.Time `json:"createdAt"    orm:"created_at"    description:"播放时间"`           // 播放时间
	UpdatedAt    *gtime.Time `json:"updatedAt"    orm:"updated_at"    description:"更新时间"`           // 更新时间
}
