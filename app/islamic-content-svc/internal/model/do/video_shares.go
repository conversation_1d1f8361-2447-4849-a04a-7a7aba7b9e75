// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// VideoShares is the golang structure of table video_shares for DAO operations like Where/Data.
type VideoShares struct {
	g.Meta        `orm:"table:video_shares, do:true"`
	Id            interface{} // 主键ID
	UserId        interface{} // 用户ID，0表示未登录用户
	VideoId       interface{} // 视频ID
	SharePlatform interface{} // 分享平台：wechat, facebook, twitter, whatsapp等
	CreatedAt     *gtime.Time // 分享时间
}
