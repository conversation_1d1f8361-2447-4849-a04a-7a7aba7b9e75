// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// VideoCategoryLanguages is the golang structure of table video_category_languages for DAO operations like Where/Data.
type VideoCategoryLanguages struct {
	g.Meta      `orm:"table:video_category_languages, do:true"`
	Id          interface{} // 主键ID
	CategoryId  interface{} // 分类ID
	LanguageId  interface{} // 语言ID：0-中文，1-英文，2-印尼语
	Name        interface{} // 分类名称
	Description interface{} // 分类描述
	CreatedAt   *gtime.Time // 创建时间
	UpdatedAt   *gtime.Time // 更新时间
}
