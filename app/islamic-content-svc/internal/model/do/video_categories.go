// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// VideoCategories is the golang structure of table video_categories for DAO operations like Where/Data.
type VideoCategories struct {
	g.Meta     `orm:"table:video_categories, do:true"`
	Id         interface{} // 主键ID
	VideoCount interface{} // 分类下视频数量
	Remark     interface{} // 备注
	CreatedAt  *gtime.Time // 创建时间
	UpdatedAt  *gtime.Time // 更新时间
}
