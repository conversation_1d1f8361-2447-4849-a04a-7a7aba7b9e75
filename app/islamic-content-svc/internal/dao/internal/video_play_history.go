// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// VideoPlayHistoryDao is the data access object for the table video_play_history.
type VideoPlayHistoryDao struct {
	table    string                  // table is the underlying table name of the DAO.
	group    string                  // group is the database configuration group name of the current DAO.
	columns  VideoPlayHistoryColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler      // handlers for customized model modification.
}

// VideoPlayHistoryColumns defines and stores column names for the table video_play_history.
type VideoPlayHistoryColumns struct {
	Id           string // 主键ID
	UserId       string // 用户ID
	VideoId      string // 视频ID
	PlayPosition string // 播放位置(秒)
	PlayDuration string // 本次播放时长(秒)
	IsCompleted  string // 是否播放完成，0-否，1-是
	CreatedAt    string // 播放时间
	UpdatedAt    string // 更新时间
}

// videoPlayHistoryColumns holds the columns for the table video_play_history.
var videoPlayHistoryColumns = VideoPlayHistoryColumns{
	Id:           "id",
	UserId:       "user_id",
	VideoId:      "video_id",
	PlayPosition: "play_position",
	PlayDuration: "play_duration",
	IsCompleted:  "is_completed",
	CreatedAt:    "created_at",
	UpdatedAt:    "updated_at",
}

// NewVideoPlayHistoryDao creates and returns a new DAO object for table data access.
func NewVideoPlayHistoryDao(handlers ...gdb.ModelHandler) *VideoPlayHistoryDao {
	return &VideoPlayHistoryDao{
		group:    "default",
		table:    "video_play_history",
		columns:  videoPlayHistoryColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *VideoPlayHistoryDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *VideoPlayHistoryDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *VideoPlayHistoryDao) Columns() VideoPlayHistoryColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *VideoPlayHistoryDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *VideoPlayHistoryDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *VideoPlayHistoryDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
