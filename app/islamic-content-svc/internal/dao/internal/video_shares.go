// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// VideoSharesDao is the data access object for the table video_shares.
type VideoSharesDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  VideoSharesColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// VideoSharesColumns defines and stores column names for the table video_shares.
type VideoSharesColumns struct {
	Id            string // 主键ID
	UserId        string // 用户ID，0表示未登录用户
	VideoId       string // 视频ID
	SharePlatform string // 分享平台：wechat, facebook, twitter, whatsapp等
	CreatedAt     string // 分享时间
}

// videoSharesColumns holds the columns for the table video_shares.
var videoSharesColumns = VideoSharesColumns{
	Id:            "id",
	UserId:        "user_id",
	VideoId:       "video_id",
	SharePlatform: "share_platform",
	CreatedAt:     "created_at",
}

// NewVideoSharesDao creates and returns a new DAO object for table data access.
func NewVideoSharesDao(handlers ...gdb.ModelHandler) *VideoSharesDao {
	return &VideoSharesDao{
		group:    "default",
		table:    "video_shares",
		columns:  videoSharesColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *VideoSharesDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *VideoSharesDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *VideoSharesDao) Columns() VideoSharesColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *VideoSharesDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *VideoSharesDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *VideoSharesDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
