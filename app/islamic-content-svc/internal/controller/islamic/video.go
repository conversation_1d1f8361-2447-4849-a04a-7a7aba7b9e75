package islamic

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"

	"halalplus/api/common"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/service"
)

type ControllerVideo struct {
	v1.UnimplementedVideoServiceServer
}

// PlaylistList 获取视频播放列表
func (*ControllerVideo) PlaylistList(ctx context.Context, req *v1.PlaylistListReq) (res *v1.PlaylistListRes, err error) {
	// 设置默认分页参数
	page := int(req.Page.Page)
	size := int(req.Page.Size)
	if page <= 0 {
		page = 1
	}
	if size <= 0 {
		size = 10
	}

	output, err := service.Video().PlaylistList(ctx, req.LanguageId, page, size)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	// 转换格式
	var playlistList []*v1.PlaylistItem
	err = gconv.Structs(output.List, &playlistList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.PlaylistListRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.PlaylistListResData{
			List: playlistList,
			Page: &common.PageResponse{
				Page:  int32(output.Page.Page),
				Size:  int32(output.Page.Size),
				Total: int32(output.Page.Total),
			},
		},
	}, nil
}

// VideoList 获取视频列表
func (*ControllerVideo) VideoList(ctx context.Context, req *v1.VideoListReq) (res *v1.VideoListRes, err error) {
	// 设置默认分页参数
	page := int(req.Page.Page)
	size := int(req.Page.Size)
	if page <= 0 {
		page = 1
	}
	if size <= 0 {
		size = 10
	}

	// 构建输入参数
	input := &model.VideoListInput{
		Title:      req.Title,
		SortBy:     req.SortBy,
		SortOrder:  req.SortOrder,
		LanguageId: 0, // 暂时使用语言ID 0，后续可以从请求中获取
		Page:       page,
		Size:       size,
	}

	// 处理可选参数
	if req.CategoryId != nil {
		categoryId := uint32(req.CategoryId.Value)
		input.CategoryId = &categoryId
	}
	if req.PlaylistId != nil {
		playlistId := uint32(req.PlaylistId.Value)
		input.PlaylistId = &playlistId
	}

	// 调用service层
	output, err := service.Video().VideoList(ctx, input)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	// 转换为protobuf格式
	var videoList []*v1.VideoListItem
	err = gconv.Structs(output.List, &videoList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	// 构建响应数据
	resData := &v1.VideoListResData{
		List: videoList,
		Page: &common.PageResponse{
			Page:  int32(output.Page.Page),
			Size:  int32(output.Page.Size),
			Total: int32(output.Page.Total),
		},
	}

	// 如果有播放列表信息，添加到响应中
	if output.Playlist != nil {
		resData.Playlist = &v1.PlaylistBasicInfo{
			PlaylistId: output.Playlist.PlaylistId,
			Name:       output.Playlist.Name,
		}
	}

	return &v1.VideoListRes{
		Code: 200,
		Msg:  "success",
		Data: resData,
	}, nil
}

// VideoDetail 获取视频详情
func (*ControllerVideo) VideoDetail(ctx context.Context, req *v1.VideoDetailReq) (res *v1.VideoDetailRes, err error) {
	// 调用service层
	output, err := service.Video().VideoDetail(ctx, req.VideoId, 0, 0) // 暂时使用语言ID 0和用户ID 0
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	// 转换为protobuf格式
	var video *v1.Video
	err = gconv.Struct(output.Video, &video)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.VideoDetailRes{
		Code: 200,
		Msg:  "success",
		Data: video,
	}, nil
}

// RecommendedVideoList 获取推荐视频列表
func (*ControllerVideo) RecommendedVideoList(ctx context.Context, req *v1.RecommendedVideoListReq) (res *v1.RecommendedVideoListRes, err error) {
	// 设置默认分页参数
	page := int(req.Page.Page)
	size := int(req.Page.Size)
	if page <= 0 {
		page = 1
	}
	if size <= 0 {
		size = 10
	}

	// 构建输入参数
	input := &model.RecommendedVideoListInput{
		LanguageId: 0, // 暂时使用语言ID 0，后续可以从请求中获取
		Page:       page,
		Size:       size,
	}

	// 处理可选参数
	if req.CategoryId != nil {
		categoryId := uint32(req.CategoryId.Value)
		input.CategoryId = &categoryId
	}

	// 调用service层
	output, err := service.Video().RecommendedVideoList(ctx, input)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	// 转换为protobuf格式
	var videoList []*v1.VideoListItem
	err = gconv.Structs(output.List, &videoList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.RecommendedVideoListRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.RecommendedVideoListResData{
			List: videoList,
			Page: &common.PageResponse{
				Page:  int32(output.Page.Page),
				Size:  int32(output.Page.Size),
				Total: int32(output.Page.Total),
			},
		},
	}, nil
}

// VideoCollect 视频收藏/取消收藏
func (*ControllerVideo) VideoCollect(ctx context.Context, req *v1.VideoCollectReq) (res *v1.VideoCollectRes, err error) {
	// 构建输入参数
	input := &model.VideoCollectInput{
		UserId:  0, // 暂时使用用户ID 0，后续需要从请求上下文中获取
		VideoId: req.VideoId,
		IsAdd:   req.IsAdd,
	}

	// 调用service层
	err = service.Video().VideoCollect(ctx, input)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.VideoCollectRes{
		Code: 200,
		Msg:  "success",
	}, nil
}

// VideoCollectList 获取用户收藏的视频列表
func (*ControllerVideo) VideoCollectList(ctx context.Context, req *v1.VideoCollectListReq) (res *v1.VideoCollectListRes, err error) {
	// 设置默认分页参数
	page := int(req.Page.Page)
	size := int(req.Page.Size)
	if page <= 0 {
		page = 1
	}
	if size <= 0 {
		size = 10
	}

	// 调用service层
	output, err := service.Video().VideoCollectList(ctx, 0, 0, page, size) // 暂时使用用户ID 0和语言ID 0
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	// 转换为protobuf格式
	var videoList []*v1.VideoListItem
	err = gconv.Structs(output.List, &videoList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.VideoCollectListRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.VideoCollectListResData{
			List: videoList,
			Page: &common.PageResponse{
				Page:  int32(output.Page.Page),
				Size:  int32(output.Page.Size),
				Total: int32(output.Page.Total),
			},
		},
	}, nil
}
